package com.mitdd.gazetracker.net

import com.airdoc.component.common.cache.MMKVManager
import com.mitdd.gazetracker.common.CommonPreference

/**
 * FileName: UrlConfig
 * Author by lilin,Date on 2024/10/8 15:14
 * PS: Not easy to write code, please indicate.
 */
object UrlConfig {

    //APP主接口域名
    const val MAIN_DOMAIN_TEST = "https://test-dt.airdoc.com"
    const val MAIN_DOMAIN_RELEASE = "https://dt.airdoc.com"
    const val _DOMAIN_RELEASE = "https://dt.airdoc.com"
    var MAIN_DOMAIN = MMKVManager.decodeString(CommonPreference.MAIN_DOMAIN)?: MAIN_DOMAIN_RELEASE

    const val ADA_DOMAIN_TEST = "https://test-ada.airdoc.com"
    const val ADA_DOMAIN_RELEASE = "https://ada.airdoc.com"
    var ADA_DOMAIN = if (MAIN_DOMAIN == MAIN_DOMAIN_RELEASE) ADA_DOMAIN_RELEASE else ADA_DOMAIN_TEST

    const val RES_DOMAIN = "https://ada-res.airdoc.com"

    //Movement模块接口域名
    const val MOVEMENT_DOMAIN = "https://61eb9230.r1.cpolar.xyz"

    //APP海外接口域名
    const val OVERSEAS_DOMAIN_TEST = "https://test-api.babyeye.com"
    const val OVERSEAS_DOMAIN_RELEASE = "https://api.babyeye.com"
    var OVERSEAS_DOMAIN = if (MAIN_DOMAIN == MAIN_DOMAIN_RELEASE) OVERSEAS_DOMAIN_RELEASE else OVERSEAS_DOMAIN_TEST

    fun switchDomain(domain:String){
        when(domain){
            "RELEASE" ->{
                MAIN_DOMAIN = MAIN_DOMAIN_RELEASE
                MMKVManager.encodeString(CommonPreference.MAIN_DOMAIN, MAIN_DOMAIN_RELEASE)
                ADA_DOMAIN = ADA_DOMAIN_RELEASE
                OVERSEAS_DOMAIN = OVERSEAS_DOMAIN_RELEASE
            }
            "TEST" ->{
                MAIN_DOMAIN = MAIN_DOMAIN_TEST
                MMKVManager.encodeString(CommonPreference.MAIN_DOMAIN, MAIN_DOMAIN_TEST)
                ADA_DOMAIN = ADA_DOMAIN_TEST
                OVERSEAS_DOMAIN = OVERSEAS_DOMAIN_TEST
            }
        }
    }

}