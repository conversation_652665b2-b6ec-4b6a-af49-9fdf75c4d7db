package com.mitdd.gazetracker.movement.vm

import android.graphics.PointF
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.google.gson.Gson
import com.mitdd.gazetracker.gaze.bean.GazePoint
import com.mitdd.gazetracker.movement.bean.FollowAbilityAdd
import com.mitdd.gazetracker.movement.bean.FileUploadResponse
import com.mitdd.gazetracker.movement.bean.FileUploadData
import com.mitdd.gazetracker.movement.repository.FollowAbilityRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.*

/**
 * FileName: FollowAbilityViewModel
 * Author by AI Assistant, Date on 2025/6/23
 * PS: Not easy to write code, please indicate.
 * 追随能力检测ViewModel
 */
class FollowAbilityViewModel : ViewModel() {

    companion object {
        private val TAG = FollowAbilityViewModel::class.java.simpleName
    }

    private val followAbilityRepository = FollowAbilityRepository()
    private val gson = Gson()

    // 图片上传结果
    val uploadImageResultLiveData = MutableLiveData<FileUploadResponse?>()
    
    // 检测结果提交结果
    val submitResultLiveData = MutableLiveData<FollowAbilityAdd?>()

    /**
     * 上传图片
     * @param imageFile 图片文件
     */
    fun uploadImage(imageFile: File) {
        Logger.d(TAG, msg = "开始上传追随能力检测结果图片: ${imageFile.name}")
        
        viewModelScope.launch {
            try {
                val requestFile = imageFile.asRequestBody("image/png".toMediaTypeOrNull())
                val body = MultipartBody.Part.createFormData("file", imageFile.name, requestFile)

                MutableStateFlow(followAbilityRepository.uploadImage(body)).collectResponse {
                    onSuccess = { result, _, _ ->
                        Logger.d(TAG, msg = "追随能力检测结果图片上传成功")
                        Logger.d(TAG, msg = "图片URL: ${result?.data?.url}")
                        uploadImageResultLiveData.postValue(result)
                    }
                    onDataEmpty = { _, _ ->
                        Logger.e(TAG, msg = "追随能力检测结果图片上传失败 - 返回数据为空")
                        uploadImageResultLiveData.postValue(null)
                    }
                    onFailed = { errorCode, errorMsg ->
                        Logger.e(TAG, msg = "追随能力检测结果图片上传失败 - errorCode: $errorCode, errorMsg: $errorMsg")
                        uploadImageResultLiveData.postValue(null)
                    }
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "图片上传异常: ${e.message}")
                uploadImageResultLiveData.postValue(null)
            }
        }
    }

    /**
     * 提交追随能力检测结果
     * @param patientId 患者ID
     * @param followPathPoints 追随路径点列表（贝塞尔曲线控制点）
     * @param gazePoints 视线轨迹点列表
     * @param duration 测试持续时间(毫秒)
     * @param notes 测试备注
     * @param imageUrl 图片URL
     */
    fun submitFollowAbilityResult(
        patientId: Long,
        followPathPoints: List<List<PointF>>,
        gazePoints: List<GazePoint>,
        duration: Int = 60000,
        notes: String = "追随能力测试",
        imageUrl: String? = null
    ) {
        Logger.d(TAG, msg = "开始提交追随能力检测结果")
        Logger.d(TAG, msg = "患者ID: $patientId, 路径段数量: ${followPathPoints.size}, 轨迹点数量: ${gazePoints.size}")
        Logger.d(TAG, msg = "测试持续时间: ${duration}ms, 备注: $notes")
        Logger.d(TAG, msg = "图片URL: $imageUrl")
        
        viewModelScope.launch {
            try {
                // 构建提交参数
                val params = buildSubmitParams(
                    patientId, followPathPoints, gazePoints, duration, notes, imageUrl
                )

                Logger.d(TAG, msg = "提交参数构建完成，开始发送请求")

                MutableStateFlow(followAbilityRepository.submitFollowAbilityResult(params)).collectResponse {
                    onSuccess = { result, _, _ ->
                        Logger.d(TAG, msg = "追随能力检测结果提交成功")
                        Logger.d(TAG, msg = "返回记录ID: ${result?.data}")
                        submitResultLiveData.postValue(result)
                    }
                    onDataEmpty = { _, _ ->
                        Logger.e(TAG, msg = "追随能力检测结果提交失败 - 返回数据为空")
                        submitResultLiveData.postValue(null)
                    }
                    onFailed = { errorCode, errorMsg ->
                        Logger.e(TAG, msg = "追随能力检测结果提交失败 - errorCode: $errorCode, errorMsg: $errorMsg")
                        submitResultLiveData.postValue(null)
                    }
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "追随能力检测结果提交异常: ${e.message}")
                submitResultLiveData.postValue(null)
            }
        }
    }

    /**
     * 构建提交参数
     */
    private fun buildSubmitParams(
        patientId: Long,
        followPathPoints: List<List<PointF>>,
        gazePoints: List<GazePoint>,
        duration: Int,
        notes: String,
        imageUrl: String?
    ): HashMap<String, Any> {
        
        Logger.d(TAG, msg = "开始构建追随能力检测提交参数")
        
        // 构建测试信息
        val testInfo = hashMapOf<String, Any>(
            "testType" to "FOLLOW_ABILITY",
            "testSequence" to "02",
            "testDate" to SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault()).format(Date()),
            "duration" to duration,
            "calibrationParams" to "\"标准校准\"",
            "environmentInfo" to "\"室内光线充足\"",
            "notes" to notes
        )

        // 添加图片URL（如果有）
        imageUrl?.let {
            testInfo["imageUrl"] = it
            Logger.d(TAG, msg = "图片URL已添加到testInfo: $it")
        } ?: run {
            Logger.d(TAG, msg = "图片URL为空，未添加到testInfo")
        }

        // 构建视线轨迹数据
        val gazeTrajectory = gazePoints.mapIndexed { index, point ->
            hashMapOf<String, Any>(
                "index" to (index + 1),
                "x" to (point.x ?: 0.5f),
                "y" to (point.y ?: 0.5f),
                "distance" to (point.dist ?: 0.0f),
                "duration" to (point.duration ?: 0),
                "timestamp" to (point.timestamp ?: System.currentTimeMillis()),
                "isValid" to point.checkValid(),
                "skew" to false
            )
        }

        // 构建追随能力特有数据
        val followAbilityData = buildFollowAbilityData(followPathPoints, gazePoints)

        // 构建结果数据
        val resultData = hashMapOf<String, Any>(
            "gazeTrajectory" to gazeTrajectory,
            "followAbilityData" to followAbilityData
        )

        // 构建完整参数
        val params = hashMapOf<String, Any>(
            "patientId" to patientId,
            "testInfo" to testInfo,
            "resultData" to resultData
        )

        Logger.d(TAG, msg = "追随能力检测提交参数构建完成")
        Logger.d(TAG, msg = "参数概要 - 患者ID: $patientId, 路径段: ${followPathPoints.size}, 轨迹点: ${gazePoints.size}")

        return params
    }

    /**
     * 构建追随能力特有数据
     */
    private fun buildFollowAbilityData(
        followPathPoints: List<List<PointF>>,
        gazePoints: List<GazePoint>
    ): HashMap<String, Any> {

        Logger.d(TAG, msg = "开始构建追随能力特有数据")

        // 转换路径点为JSON格式
        val pathPointsJson = gson.toJson(followPathPoints.map { segment ->
            segment.map { point -> listOf(point.x, point.y) }
        })

        // 转换实际视线点为JSON格式
        val actualGazePointsJson = gson.toJson(gazePoints.map { point ->
            mapOf(
                "x" to (point.x ?: 0.5f),
                "y" to (point.y ?: 0.5f),
                "timestamp" to (point.timestamp ?: System.currentTimeMillis()),
                "duration" to (point.duration ?: 0),
                "distance" to (point.dist ?: 0.0f)
            )
        })

        // 计算追随能力指标
        val trackingAccuracy = calculateTrackingAccuracy(followPathPoints, gazePoints)
        val averageError = calculateAverageError(followPathPoints, gazePoints)
        val reactionTime = calculateReactionTime(gazePoints)
        val smoothnessScore = calculateSmoothnessScore(gazePoints)
        val completionRate = calculateCompletionRate(followPathPoints, gazePoints)
        val pathLength = calculatePathLength(followPathPoints)
        val actualPathLength = calculateActualPathLength(gazePoints)
        val velocityConsistency = calculateVelocityConsistency(gazePoints)

        // 构建贝塞尔曲线数据
        val bezierCurveData = gson.toJson(followPathPoints.map { segment ->
            if (segment.size >= 4) {
                mapOf(
                    "startPoint" to listOf(segment[0].x, segment[0].y),
                    "controlPoint1" to listOf(segment[1].x, segment[1].y),
                    "controlPoint2" to listOf(segment[2].x, segment[2].y),
                    "endPoint" to listOf(segment[3].x, segment[3].y)
                )
            } else {
                mapOf("points" to segment.map { listOf(it.x, it.y) })
            }
        })

        val followAbilityData = hashMapOf<String, Any>(
            "followPathPoints" to pathPointsJson,
            "actualGazePoints" to actualGazePointsJson,
            "trackingAccuracy" to trackingAccuracy,
            "averageError" to averageError,
            "reactionTime" to reactionTime,
            "smoothnessScore" to smoothnessScore,
            "completionRate" to completionRate,
            "bezierCurveData" to bezierCurveData,
            "followCount" to followPathPoints.size,
            "successfulFollows" to calculateSuccessfulFollows(followPathPoints, gazePoints),
            "pathLength" to pathLength,
            "actualPathLength" to actualPathLength,
            "velocityConsistency" to velocityConsistency
        )

        Logger.d(TAG, msg = "追随能力特有数据构建完成")
        Logger.d(TAG, msg = "跟踪准确率: $trackingAccuracy%, 平均误差: $averageError, 完成率: $completionRate%")

        return followAbilityData
    }

    /**
     * 计算跟踪准确率
     */
    private fun calculateTrackingAccuracy(
        followPathPoints: List<List<PointF>>,
        gazePoints: List<GazePoint>
    ): Double {
        if (gazePoints.isEmpty() || followPathPoints.isEmpty()) return 0.0

        var totalAccuracy = 0.0
        var validPoints = 0

        gazePoints.forEach { gazePoint ->
            val gazeX = gazePoint.x ?: return@forEach
            val gazeY = gazePoint.y ?: return@forEach

            // 找到最近的路径点
            var minDistance = Double.MAX_VALUE
            followPathPoints.forEach { segment ->
                segment.forEach { pathPoint ->
                    val distance = sqrt((gazeX - pathPoint.x).pow(2) + (gazeY - pathPoint.y).pow(2))
                    if (distance < minDistance) {
                        minDistance = distance.toDouble()
                    }
                }
            }

            // 计算准确率（距离越小准确率越高）
            val accuracy = max(0.0, 100.0 - minDistance * 1000) // 假设阈值为0.1
            totalAccuracy += accuracy
            validPoints++
        }

        return if (validPoints > 0) totalAccuracy / validPoints else 0.0
    }

    /**
     * 计算平均误差距离
     */
    private fun calculateAverageError(
        followPathPoints: List<List<PointF>>,
        gazePoints: List<GazePoint>
    ): Double {
        if (gazePoints.isEmpty() || followPathPoints.isEmpty()) return 0.0

        var totalError = 0.0
        var validPoints = 0

        gazePoints.forEach { gazePoint ->
            val gazeX = gazePoint.x ?: return@forEach
            val gazeY = gazePoint.y ?: return@forEach

            // 找到最近的路径点距离
            var minDistance = Double.MAX_VALUE
            followPathPoints.forEach { segment ->
                segment.forEach { pathPoint ->
                    val distance = sqrt((gazeX - pathPoint.x).pow(2) + (gazeY - pathPoint.y).pow(2))
                    if (distance < minDistance) {
                        minDistance = distance.toDouble()
                    }
                }
            }

            totalError += minDistance
            validPoints++
        }

        return if (validPoints > 0) totalError / validPoints else 0.0
    }

    /**
     * 计算反应时间
     */
    private fun calculateReactionTime(gazePoints: List<GazePoint>): Double {
        if (gazePoints.size < 2) return 0.0

        // 计算第一个有效视线点的反应时间
        val firstValidPoint = gazePoints.firstOrNull { it.checkValid() }
        return firstValidPoint?.timestamp?.let { timestamp ->
            // 假设测试开始时间为第一个点的时间戳减去一些延迟
            max(0.0, (timestamp - (gazePoints.firstOrNull()?.timestamp ?: timestamp)).toDouble())
        } ?: 0.0
    }

    /**
     * 计算平滑度评分
     */
    private fun calculateSmoothnessScore(gazePoints: List<GazePoint>): Double {
        if (gazePoints.size < 3) return 0.0

        var totalVariation = 0.0
        var validSegments = 0

        for (i in 1 until gazePoints.size - 1) {
            val prev = gazePoints[i - 1]
            val curr = gazePoints[i]
            val next = gazePoints[i + 1]

            val prevX = prev.x ?: continue
            val prevY = prev.y ?: continue
            val currX = curr.x ?: continue
            val currY = curr.y ?: continue
            val nextX = next.x ?: continue
            val nextY = next.y ?: continue

            // 计算方向变化
            val angle1 = atan2(currY - prevY, currX - prevX)
            val angle2 = atan2(nextY - currY, nextX - currX)
            val angleDiff = abs(angle2 - angle1)

            totalVariation += angleDiff
            validSegments++
        }

        val averageVariation = if (validSegments > 0) totalVariation / validSegments else 0.0
        // 平滑度评分：变化越小评分越高
        return max(0.0, 100.0 - averageVariation * 180 / PI * 10)
    }

    /**
     * 计算完成率
     */
    private fun calculateCompletionRate(
        followPathPoints: List<List<PointF>>,
        gazePoints: List<GazePoint>
    ): Double {
        if (followPathPoints.isEmpty()) return 0.0

        val totalPathSegments = followPathPoints.size
        var completedSegments = 0

        // 简化计算：如果有足够的视线点，认为完成了追随
        if (gazePoints.size >= totalPathSegments * 10) { // 假设每段路径需要至少10个点
            completedSegments = totalPathSegments
        } else {
            completedSegments = (gazePoints.size / 10).coerceAtMost(totalPathSegments)
        }

        return (completedSegments.toDouble() / totalPathSegments) * 100.0
    }

    /**
     * 计算成功追随次数
     */
    private fun calculateSuccessfulFollows(
        followPathPoints: List<List<PointF>>,
        gazePoints: List<GazePoint>
    ): Int {
        if (followPathPoints.isEmpty() || gazePoints.isEmpty()) return 0

        // 基于跟踪准确率判断成功次数
        val accuracy = calculateTrackingAccuracy(followPathPoints, gazePoints)
        return if (accuracy > 60.0) followPathPoints.size else (followPathPoints.size * accuracy / 100).toInt()
    }

    /**
     * 计算路径总长度
     */
    private fun calculatePathLength(followPathPoints: List<List<PointF>>): Double {
        var totalLength = 0.0

        followPathPoints.forEach { segment ->
            for (i in 1 until segment.size) {
                val prev = segment[i - 1]
                val curr = segment[i]
                val distance = sqrt((curr.x - prev.x).pow(2) + (curr.y - prev.y).pow(2))
                totalLength += distance
            }
        }

        return totalLength
    }

    /**
     * 计算实际追随路径长度
     */
    private fun calculateActualPathLength(gazePoints: List<GazePoint>): Double {
        if (gazePoints.size < 2) return 0.0

        var totalLength = 0.0

        for (i in 1 until gazePoints.size) {
            val prev = gazePoints[i - 1]
            val curr = gazePoints[i]

            val prevX = prev.x ?: continue
            val prevY = prev.y ?: continue
            val currX = curr.x ?: continue
            val currY = curr.y ?: continue

            val distance = sqrt((currX - prevX).pow(2) + (currY - prevY).pow(2))
            totalLength += distance
        }

        return totalLength
    }

    /**
     * 计算速度一致性
     */
    private fun calculateVelocityConsistency(gazePoints: List<GazePoint>): Double {
        if (gazePoints.size < 3) return 0.0

        val velocities = mutableListOf<Double>()

        for (i in 1 until gazePoints.size) {
            val prev = gazePoints[i - 1]
            val curr = gazePoints[i]

            val prevX = prev.x ?: continue
            val prevY = prev.y ?: continue
            val currX = curr.x ?: continue
            val currY = curr.y ?: continue

            val distance = sqrt((currX - prevX).pow(2) + (currY - prevY).pow(2))
            val timeDiff = (curr.timestamp ?: 0) - (prev.timestamp ?: 0)

            if (timeDiff > 0) {
                val velocity = distance / (timeDiff / 1000.0) // 像素/秒
                velocities.add(velocity)
            }
        }

        if (velocities.isEmpty()) return 0.0

        val averageVelocity = velocities.average()
        val variance = velocities.map { (it - averageVelocity).pow(2) }.average()
        val standardDeviation = sqrt(variance)

        // 一致性评分：标准差越小一致性越高
        return max(0.0, 100.0 - standardDeviation * 10)
    }
}
