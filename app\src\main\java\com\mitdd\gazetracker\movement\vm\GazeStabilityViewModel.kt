package com.mitdd.gazetracker.movement.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.google.gson.Gson
import com.mitdd.gazetracker.gaze.bean.GazePoint
import com.mitdd.gazetracker.movement.bean.GazeStabilityAdd
import com.mitdd.gazetracker.movement.bean.FileUploadResponse
import com.mitdd.gazetracker.movement.repository.GazeStabilityRepository
import com.mitdd.gazetracker.movement.vm.EMPatientViewModel.Companion
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.*

/**
 * FileName: GazeStabilityViewModel
 * Author by lilin,Date on 2025/6/20 10:30
 * PS: Not easy to write code, please indicate.
 * 注视稳定性检测ViewModel
 */
class GazeStabilityViewModel : ViewModel() {

    companion object {
        private val TAG = GazeStabilityViewModel::class.java.simpleName
    }

    private val gazeStabilityRepository = GazeStabilityRepository()
    private val gson = Gson()

    /**
     * 提交结果LiveData
     */
    val submitResultLiveData = MutableLiveData<GazeStabilityAdd?>()

    /**
     * 图片上传结果LiveData
     */
    val uploadImageResultLiveData = MutableLiveData<FileUploadResponse?>()

    /**
     * 上传图片
     * @param imageFile 图片文件
     */
    fun uploadImage(imageFile: File) {
        Logger.d(TAG, msg = "开始上传图片: ${imageFile.name}")

        viewModelScope.launch {
            try {
                // 创建RequestBody
                val requestFile = imageFile.asRequestBody("image/*".toMediaTypeOrNull())
                val body = MultipartBody.Part.createFormData("file", imageFile.name, requestFile)

                // 调用API
                MutableStateFlow(gazeStabilityRepository.uploadImage(body)).collectResponse {
                    onSuccess = { result, _, _ ->
                        Logger.d(TAG, msg = "图片上传成功")
                        Logger.d(TAG, msg = "返回URL: ${result?.data?.url}")
                        uploadImageResultLiveData.postValue(result)
                    }
                    onDataEmpty = { _, _ ->
                        Logger.e(TAG, msg = "图片上传失败 - 返回数据为空")
                        uploadImageResultLiveData.postValue(null)
                    }
                    onFailed = { errorCode, errorMsg ->
                        Logger.e(TAG, msg = "图片上传失败 - errorCode: $errorCode, errorMsg: $errorMsg")
                        uploadImageResultLiveData.postValue(null)
                    }
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "图片上传异常: ${e.message}")
                uploadImageResultLiveData.postValue(null)
            }
        }
    }

    /**
     * 提交注视稳定性检测结果
     * @param patientId 患者ID
     * @param targetX 目标点X坐标
     * @param targetY 目标点Y坐标
     * @param gazePoints 视线轨迹点列表
     * @param duration 测试持续时间(毫秒)
     * @param notes 测试备注
     * @param imageUrl 图片URL
     */
    fun submitGazeStabilityResult(
        patientId: Long,
        targetX: Float,
        targetY: Float,
        gazePoints: List<GazePoint>,
        duration: Int = 30000,
        notes: String = "注视稳定性测试",
        imageUrl: String? = null,
        targetPointRadius: Int = 25,
        gazePointRadius: Int = 12,
        loopRadiusIncreases: Int = 37,
        loopsCount: Int = 4
    ) {
        Logger.d(TAG, msg = "开始提交注视稳定性检测结果")
        Logger.d(TAG, msg = "患者ID: $patientId, 目标点: ($targetX, $targetY), 轨迹点数量: ${gazePoints.size}")

        viewModelScope.launch {
            try {
                // 构建请求参数
                val params = buildSubmitParams(patientId, targetX, targetY, gazePoints, duration, notes, imageUrl, targetPointRadius, gazePointRadius, loopRadiusIncreases, loopsCount)
                Logger.d(TAG, msg = "提交参数: ${gson.toJson(params)}")

                // 调用API
                MutableStateFlow(gazeStabilityRepository.submitGazeStabilityResult(params)).collectResponse {
                    onSuccess = { result, _, _ ->
                        Logger.d(TAG, msg = "注视稳定性检测结果提交成功")
                        Logger.d(TAG, msg = "返回记录ID: \${result?.data}")
                        submitResultLiveData.postValue(result)
                    }
                    onDataEmpty = { _, _ ->
                        Logger.e(TAG, msg = "注视稳定性检测结果提交失败 - 返回数据为空")
                        submitResultLiveData.postValue(null)
                    }
                    onFailed = { errorCode, errorMsg ->
                        Logger.e(TAG, msg = "注视稳定性检测结果提交失败 - errorCode: $errorCode, errorMsg: $errorMsg")
                        submitResultLiveData.postValue(null)
                    }
                }
            } catch (e: Exception) {
                submitResultLiveData.postValue(null)
            }
        }
    }

    /**
     * 构建提交参数
     */
    private fun buildSubmitParams(
        patientId: Long,
        targetX: Float,
        targetY: Float,
        gazePoints: List<GazePoint>,
        duration: Int,
        notes: String,
        imageUrl: String? = null,
        targetPointRadius: Int = 25,
        gazePointRadius: Int = 12,
        loopRadiusIncreases: Int = 37,
        loopsCount: Int = 4
    ): HashMap<String, Any> {
        val currentTime = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault()).format(Date())
        
        // 构建视线轨迹数据（保持原有简单结构）
        val gazeTrajectory = gazePoints.mapIndexed { index, point ->
            hashMapOf<String, Any>(
                "index" to (index + 1),
                "x" to (point.x ?: 0.5f),
                "y" to (point.y ?: 0.5f),
                "distance" to (point.dist ?: 50.0f),
                "duration" to (point.duration ?: 100)
            )
        }

        // 构建增强的轨迹数据用于JSON字符串（包含位置分析信息）
        val enhancedGazeTrajectory = gazePoints.mapIndexed { index, point ->
            val gazeX = point.x ?: 0.5f
            val gazeY = point.y ?: 0.5f

            // 计算相对于目标点的位置信息
            val positionInfo = calculateGazePositionInfo(gazeX, gazeY, targetX, targetY, targetPointRadius, loopRadiusIncreases, loopsCount)

            hashMapOf<String, Any>(
                "index" to (index + 1),
                "x" to gazeX,
                "y" to gazeY,
                "distance" to (point.dist ?: 50.0f),
                "duration" to (point.duration ?: 100),
                "relativeDistance" to positionInfo.distance,
                "direction" to positionInfo.direction,
                "directionAngle" to positionInfo.angle,
                "ringLevel" to positionInfo.ringLevel,
                "isInTargetArea" to positionInfo.isInTargetArea
            )
        }

        // 构建注视稳定性数据
        val gazeStabilityData = hashMapOf<String, Any>(
            "targetX" to targetX,
            "targetY" to targetY,
            "gazeTrajectoryJson" to gson.toJson(enhancedGazeTrajectory), // 使用增强版数据
            "targetPointRadius" to targetPointRadius,
            "gazePointRadius" to gazePointRadius,
            "loopRadiusIncreases" to loopRadiusIncreases,
            "loopsCount" to loopsCount
        )

        // 构建测试信息
        val testInfo = hashMapOf<String, Any>(
            "testType" to "GAZE_STABILITY",
            "testSequence" to "01",
            "testDate" to currentTime,
            "duration" to duration,
            "calibrationParams" to "{\"param\": 1}",
            "environmentInfo" to "{\"description\": \"室内光线充足\"}",
            "notes" to notes
        )

        // 如果有图片URL，添加到testInfo中
        imageUrl?.let {
            testInfo["imageUrl"] = it
            Logger.d(TAG, msg = "图片URL已添加到testInfo: $it")
        } ?: run {
            Logger.d(TAG, msg = "图片URL为空，未添加到testInfo")
        }

        // 构建结果数据
        val resultData = hashMapOf<String, Any>(
            "gazeTrajectory" to gazeTrajectory, // 保持原有简单结构
            "gazeStabilityData" to gazeStabilityData // gazeTrajectoryJson包含增强信息
        )

        // 构建完整请求参数
        return hashMapOf(
            "patientId" to patientId,
            "testInfo" to testInfo,
            "resultData" to resultData
        )
    }

    /**
     * 计算视点相对于目标点的位置信息
     */
    private fun calculateGazePositionInfo(
        gazeX: Float,
        gazeY: Float,
        targetX: Float,
        targetY: Float,
        targetPointRadius: Int,
        loopRadiusIncreases: Int,
        loopsCount: Int
    ): GazePositionInfo {
        // 计算相对距离（像素距离，基于屏幕尺寸）
        val deltaX = gazeX - targetX
        val deltaY = gazeY - targetY
        val pixelDistance = sqrt(deltaX * deltaX + deltaY * deltaY)

        // 计算角度（以目标点为原点，正东为0度，逆时针为正）
        val angleRadians = atan2(-deltaY, deltaX) // 注意Y轴翻转
        val angleDegrees = Math.toDegrees(angleRadians.toDouble()).toFloat()
        val normalizedAngle = if (angleDegrees < 0) angleDegrees + 360 else angleDegrees

        // 计算方位
        val direction = getDirectionFromAngle(normalizedAngle)

        // 将相对距离转换为像素距离（假设屏幕宽度为1920像素）
        val screenWidth = 1920f
        val actualPixelDistance = pixelDistance * screenWidth

        val ringLevel = calculateRingLevel(actualPixelDistance, targetPointRadius, loopRadiusIncreases, loopsCount)
        val isInTargetArea = actualPixelDistance <= targetPointRadius

        return GazePositionInfo(
            distance = pixelDistance,
            angle = normalizedAngle,
            direction = direction,
            ringLevel = ringLevel,
            isInTargetArea = isInTargetArea
        )
    }

    /**
     * 根据角度获取方位
     */
    private fun getDirectionFromAngle(angle: Float): String {
        return when {
            angle >= 337.5 || angle < 22.5 -> "东"
            angle >= 22.5 && angle < 67.5 -> "东北"
            angle >= 67.5 && angle < 112.5 -> "北"
            angle >= 112.5 && angle < 157.5 -> "西北"
            angle >= 157.5 && angle < 202.5 -> "西"
            angle >= 202.5 && angle < 247.5 -> "西南"
            angle >= 247.5 && angle < 292.5 -> "南"
            angle >= 292.5 && angle < 337.5 -> "东南"
            else -> "未知"
        }
    }

    /**
     * 计算环级别
     */
    private fun calculateRingLevel(
        distance: Float,
        targetRadius: Int,
        radiusIncreases: Int,
        maxRings: Int
    ): Int {
        if (distance <= targetRadius) {
            return 0 // 在目标区域内
        }

        for (ring in 1..maxRings) {
            val ringRadius = targetRadius + radiusIncreases * ring
            if (distance <= ringRadius) {
                return ring
            }
        }

        return maxRings + 1 // 超出所有环
    }

    /**
     * 视点位置信息数据类
     */
    private data class GazePositionInfo(
        val distance: Float,        // 相对距离（0-1范围）
        val angle: Float,           // 角度（0-360度）
        val direction: String,      // 方位（东、西南等）
        val ringLevel: Int,         // 环级别（0表示在目标区域，1-N表示第几环）
        val isInTargetArea: Boolean // 是否在目标区域内
    )
}
